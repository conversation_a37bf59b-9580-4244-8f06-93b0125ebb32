/**
 * @jest-environment jsdom
 */

import { StorageManager } from '../storage-manager';
import { PerformanceMonitor } from '../../performance/performance-monitor';

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
  writable: true,
});

// Mock IndexedDB
const mockIndexedDB = {
  open: jest.fn(),
  deleteDatabase: jest.fn(),
};

Object.defineProperty(window, 'indexedDB', {
  value: mockIndexedDB,
  writable: true,
});

// Mock performance API
const mockPerformance = {
  now: jest.fn(() => Date.now()),
  mark: jest.fn(),
  measure: jest.fn(),
  getEntriesByType: jest.fn(() => []),
  memory: {
    usedJSHeapSize: 1000000,
    totalJSHeapSize: 2000000,
    jsHeapSizeLimit: 4000000,
  },
};

Object.defineProperty(global, 'performance', {
  value: mockPerformance,
  writable: true,
});

describe('Storage Integration Tests', () => {
  let storageManager: StorageManager;
  let performanceMonitor: PerformanceMonitor;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock navigator.onLine to be true for tests
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: true,
    });

    // Reset all localStorage mocks to default behavior
    mockLocalStorage.getItem.mockReturnValue(null);
    mockLocalStorage.setItem.mockImplementation(() => {});
    mockLocalStorage.removeItem.mockImplementation(() => {});
    mockLocalStorage.clear.mockImplementation(() => {});
    mockLocalStorage.length = 0;
    mockLocalStorage.key.mockReturnValue(null);

    // Ensure window.localStorage is properly mocked
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
      configurable: true,
    });

    storageManager = new StorageManager();
    performanceMonitor = new PerformanceMonitor();

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
  });

  afterEach(() => {
    // Clean up any timers or async operations
    jest.clearAllTimers();
    jest.clearAllMocks();
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
  });

  describe('Storage Performance', () => {
    it('should measure storage operation performance', async () => {
      const testData = { key: 'value', timestamp: Date.now() };
      
      performanceMonitor.startTiming('storage-write');
      await storageManager.setItem('test-key', testData);
      performanceMonitor.endTiming('storage-write');

      expect(mockPerformance.mark).toHaveBeenCalledWith('storage-write-start');
      expect(mockPerformance.mark).toHaveBeenCalledWith('storage-write-end');
      expect(mockPerformance.measure).toHaveBeenCalledWith(
        'storage-write',
        'storage-write-start',
        'storage-write-end'
      );
    });

    it('should handle large data storage efficiently', async () => {
      const largeData = {
        images: new Array(10).fill(0).map((_, i) => ({
          id: i,
          data: 'x'.repeat(50), // 50 bytes per item instead of 1KB
          metadata: { created: Date.now(), size: 1000 }
        }))
      };

      const startTime = performance.now();
      await storageManager.setItem('large-dataset', largeData);
      const endTime = performance.now();

      const duration = endTime - startTime;
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'large-dataset',
        JSON.stringify(largeData)
      );
    });

    it('should batch multiple storage operations', async () => {
      const operations = [
        { key: 'item1', value: { data: 'test1' } },
        { key: 'item2', value: { data: 'test2' } },
        { key: 'item3', value: { data: 'test3' } },
      ];

      performanceMonitor.startTiming('batch-storage');
      
      await Promise.all(
        operations.map(op => storageManager.setItem(op.key, op.value))
      );
      
      performanceMonitor.endTiming('batch-storage');

      expect(mockLocalStorage.setItem).toHaveBeenCalledTimes(3);
      operations.forEach(op => {
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
          op.key,
          JSON.stringify(op.value)
        );
      });
    });
  });

  describe('Storage Capacity Management', () => {
    it('should handle storage quota exceeded errors', async () => {
      let setItemCallCount = 0;

      // Mock localStorage to always throw quota exceeded error, even on retry
      mockLocalStorage.setItem.mockImplementation(() => {
        setItemCallCount++;
        // Create a proper DOMException-like error
        const error = new Error('Storage quota exceeded');
        error.name = 'QuotaExceededError';
        // Make it look like a DOMException
        Object.setPrototypeOf(error, DOMException.prototype);
        throw error;
      });

      // Mock the cleanup methods to prevent infinite loops
      mockLocalStorage.length = 0; // No items in storage
      mockLocalStorage.key.mockReturnValue(null); // No keys to iterate
      mockLocalStorage.removeItem.mockImplementation(() => {});

      const testData = { large: 'x'.repeat(1000) }; // 1KB string

      await expect(
        storageManager.setItem('large-item', testData)
      ).rejects.toThrow('Storage quota exceeded');

      expect(setItemCallCount).toBe(2); // Should have been called twice (initial + retry)
    });

    it('should implement storage cleanup when quota is exceeded', async () => {
      // Mock storage to have some existing items for cleanup
      mockLocalStorage.length = 3;
      mockLocalStorage.key
        .mockReturnValueOnce('old-item-1')
        .mockReturnValueOnce('old-item-2') 
        .mockReturnValueOnce('recent-item')
        .mockReturnValue(null);

      // Mock existing items in storage - old items should be removed
      mockLocalStorage.getItem
        .mockImplementation((key: string) => {
          switch (key) {
            case 'old-item-1':
              return JSON.stringify({ timestamp: Date.now() - 86400000 }); // 1 day old
            case 'old-item-2':
              return JSON.stringify({ timestamp: Date.now() - 86400000 }); // 1 day old
            case 'recent-item':
              return JSON.stringify({ timestamp: Date.now() - 60000 }); // 1 minute old
            default:
              return null;
          }
        });

      mockLocalStorage.setItem
        .mockImplementationOnce(() => {
          const error = new DOMException('Storage quota exceeded', 'QuotaExceededError');
          throw error;
        })
        .mockImplementationOnce(() => {}); // Success after cleanup

      const newData = { data: 'new item' };

      await storageManager.setItem('new-item', newData);

      // Should have attempted cleanup by removing old items
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('old-item-1');
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('old-item-2');
      expect(mockLocalStorage.removeItem).not.toHaveBeenCalledWith('recent-item');
      
      // Should have retried the operation
      expect(mockLocalStorage.setItem).toHaveBeenCalledTimes(2);
    });

    it('should monitor storage usage', async () => {
      mockLocalStorage.length = 2;
      mockLocalStorage.key
        .mockImplementation((index: number) => {
          switch (index) {
            case 0: return 'item1';
            case 1: return 'item2';
            default: return null;
          }
        });

      mockLocalStorage.getItem
        .mockImplementation((key: string) => {
          switch (key) {
            case 'item1': return '{"data":"value1"}';
            case 'item2': return '{"data":"value2"}';
            default: return null;
          }
        });

      const usage = await storageManager.getStorageUsage();

      expect(usage.itemCount).toBe(2);
      expect(usage.estimatedSize).toBeGreaterThan(0);
      expect(typeof usage.availableSpace).toBe('number');
    });
  });

  describe('Data Persistence and Recovery', () => {
    it('should persist data across sessions', async () => {
      const sessionData = {
        cameraSettings: { exposure: 300, iso: 800 },
        lastSession: Date.now(),
      };

      await storageManager.setItem('session-data', sessionData);

      // Get what was actually stored by checking the mock calls
      const setItemCalls = mockLocalStorage.setItem.mock.calls;
      const storedData = setItemCalls.find(call => call[0] === 'session-data')?.[1];
      
      // Mock getItem to return what was actually stored
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'session-data') {
          return storedData || null;
        }
        return null;
      });

      const retrievedData = await storageManager.getItem('session-data');
      expect(retrievedData).toEqual(sessionData);
    });

    it('should handle corrupted data gracefully', async () => {
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'corrupted-item') {
          return 'invalid-json{';
        }
        return null;
      });

      const result = await storageManager.getItem('corrupted-item');
      expect(result).toBeNull();

      // Should not have attempted to clean up corrupted data automatically
      // (cleanup only happens for expired TTL items, not parse errors)
    });

    it('should implement data versioning', async () => {
      const v1Data = { version: 1, settings: { exposure: 300 } };
      const v2Data = { version: 2, settings: { exposure: 300, iso: 800 } };

      // Store v1 data
      await storageManager.setItem('versioned-data', v1Data);

      // Simulate upgrade to v2
      await storageManager.migrateData('versioned-data', v1Data, v2Data);

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'versioned-data',
        JSON.stringify(v2Data)
      );
    });
  });

  describe('Offline Storage Synchronization', () => {
    it('should queue operations when offline', async () => {
      // Mock offline state
      Object.defineProperty(navigator, 'onLine', {
        value: false,
        writable: true,
      });

      const offlineData = { id: 1, data: 'offline-item' };
      
      await storageManager.setItem('offline-item', offlineData);
      
      // Should store in offline queue
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'offline-queue',
        expect.stringContaining('offline-item')
      );
    });

    it('should sync queued operations when online', async () => {
      // Setup offline queue
      const queuedOperations = [
        { type: 'set', key: 'item1', value: { data: 'test1' } },
        { type: 'set', key: 'item2', value: { data: 'test2' } },
      ];

      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'offline-queue') {
          return JSON.stringify(queuedOperations);
        }
        return null;
      });

      // Mock online state
      Object.defineProperty(navigator, 'onLine', {
        value: true,
        writable: true,
      });

      await storageManager.syncOfflineQueue();

      // Should have processed all queued operations
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'item1',
        JSON.stringify({ data: 'test1' })
      );
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'item2',
        JSON.stringify({ data: 'test2' })
      );

      // Should have cleared the queue
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('offline-queue');
    });
  });

  describe('Performance Monitoring Integration', () => {
    it('should track storage operation metrics', async () => {
      const operations = [
        () => storageManager.setItem('perf-test-1', { data: 'test' }),
        () => storageManager.getItem('perf-test-1'),
        () => storageManager.removeItem('perf-test-1'),
      ];

      for (const operation of operations) {
        const startTime = performance.now();
        await operation();
        const endTime = performance.now();
        
        expect(endTime - startTime).toBeLessThan(1000); // Should be fast
      }

      const metrics = performanceMonitor.getMetrics();
      expect(Array.isArray(metrics)).toBe(true);
    });

    it('should detect slow storage operations', async () => {
      // Mock performance.now to simulate slow operation
      const originalNow = performance.now;
      let callCount = 0;
      performance.now = jest.fn(() => {
        callCount++;
        if (callCount === 1) return 0; // Start time
        if (callCount === 2) return 1500; // End time (1500ms later)
        return originalNow.call(performance);
      });

      const slowData = { large: 'x'.repeat(100) }; // 100 bytes instead of 1MB

      performanceMonitor.startTiming('slow-storage');
      await storageManager.setItem('slow-item', slowData);
      const duration = performanceMonitor.endTiming('slow-storage');

      // Check slow operations from performance monitor
      const slowOperations = performanceMonitor.getSlowOperations(1000);
      expect(slowOperations.length).toBeGreaterThan(0);

      // Restore original performance.now
      performance.now = originalNow;
    });

    it('should monitor memory usage during storage operations', () => {
      const initialMemory = performanceMonitor.getMemoryUsage();
      
      // Perform a few storage operations instead of 100
      const promises = Array.from({ length: 5 }, (_, i) =>
        storageManager.setItem(`item-${i}`, { data: `test-${i}` })
      );

      Promise.all(promises).then(() => {
        const finalMemory = performanceMonitor.getMemoryUsage();
        
        expect(finalMemory.used).toBeGreaterThanOrEqual(initialMemory.used);
        expect(finalMemory.total).toBeGreaterThanOrEqual(initialMemory.total);
      });
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle storage API unavailability', async () => {
      Object.defineProperty(window, 'localStorage', {
        value: undefined,
        writable: true,
      });

      const fallbackManager = new StorageManager();
      
      // Should fall back to in-memory storage
      await expect(
        fallbackManager.setItem('test', { data: 'test' })
      ).resolves.not.toThrow();

      const retrieved = await fallbackManager.getItem('test');
      expect(retrieved).toEqual({ data: 'test' });
    });

    it('should implement retry logic for failed operations', async () => {
      const testData = { retry: 'test' };

      // Track attempts using mock
      const callCounts = { count: 0 };
      mockLocalStorage.setItem.mockImplementation(() => {
        callCounts.count++;
        // Fail on first 2 attempts, succeed on 3rd
        if (callCounts.count < 3) {
          throw new Error('Temporary storage error');
        }
        // Success on third attempt
      });

      await storageManager.setItemWithRetry('retry-item', testData);

      expect(callCounts.count).toBe(3);
      expect(mockLocalStorage.setItem).toHaveBeenCalledTimes(3);
    });

    it('should handle concurrent access conflicts', async () => {
      // Reset mock to track calls
      mockLocalStorage.setItem.mockClear();
      // Set implementation to actually store data
      mockLocalStorage.setItem.mockImplementation(() => {});

      const concurrentOperations = Array.from({ length: 3 }, (_, i) =>
        storageManager.setItem(`concurrent-${i}`, { data: `test-${i}` })
      );

      await expect(
        Promise.all(concurrentOperations)
      ).resolves.not.toThrow();

      // Should have called setItem for each operation
      expect(mockLocalStorage.setItem).toHaveBeenCalledTimes(3);

      // Verify the correct keys were used
      const calls = mockLocalStorage.setItem.mock.calls;
      expect(calls.some(call => call[0] === 'concurrent-0')).toBe(true);
      expect(calls.some(call => call[0] === 'concurrent-1')).toBe(true);
      expect(calls.some(call => call[0] === 'concurrent-2')).toBe(true);
    });
  });

  describe('Data Compression and Optimization', () => {
    it('should compress large data before storage', async () => {
      // Reset mock to track calls
      mockLocalStorage.setItem.mockClear();
      // Set implementation to actually store data
      mockLocalStorage.setItem.mockImplementation(() => {});
      
      const largeData = {
        repeatedData: 'x'.repeat(10000),
        array: new Array(1000).fill('repeated-value'),
      };

      await storageManager.setItem('compressed-data', largeData, { compress: true });

      // Should have stored compressed data
      const storedCall = mockLocalStorage.setItem.mock.calls.find(
        call => call[0] === 'compressed-data'
      );
      
      expect(storedCall).toBeDefined();
      expect(storedCall[1]).toMatch(/^compressed:/);
      expect(storedCall[1].length).toBeLessThan(JSON.stringify(largeData).length);
    });

    it('should decompress data on retrieval', async () => {
      // Reset mock to track calls
      mockLocalStorage.setItem.mockClear();
      mockLocalStorage.getItem.mockClear();
      // Set implementation to actually store data
      mockLocalStorage.setItem.mockImplementation(() => {});
      mockLocalStorage.getItem.mockImplementation(() => null);
      
      const originalData = { test: 'data', array: [1, 2, 3] };
      
      // Store with compression
      await storageManager.setItem('compressed-item', originalData, { compress: true });
      
      // Mock compressed data retrieval
      const compressedData = mockLocalStorage.setItem.mock.calls.find(call => call[0] === 'compressed-item')?.[1];
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'compressed-item') {
          return compressedData;
        }
        return null;
      });

      const retrievedData = await storageManager.getItem('compressed-item');
      expect(retrievedData).toEqual(originalData);
    });
  });

  describe('Storage Analytics', () => {
    it('should track storage usage patterns', () => {
      const analytics = storageManager.getAnalytics();

      expect(analytics).toHaveProperty('totalOperations');
      expect(analytics).toHaveProperty('averageOperationTime');
      expect(analytics).toHaveProperty('errorRate');
      expect(analytics).toHaveProperty('storageEfficiency');
    });

    it('should identify storage hotspots', async () => {
      // Simulate frequent access to certain keys
      const hotKeys = ['user-settings', 'camera-config', 'recent-images'];

      for (const key of hotKeys) {
        for (let i = 0; i < 10; i++) {
          await storageManager.getItem(key);
        }
      }

      const hotspots = storageManager.getHotspots();
      expect(hotspots.length).toBeGreaterThan(0);
      expect(hotspots[0].key).toBe('user-settings');
      expect(hotspots[0].accessCount).toBe(10);
    });
  });

  describe('TTL (Time-To-Live) Functionality', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should store items with TTL and auto-expire them', async () => {
      const testData = { message: 'This will expire' };
      const ttl = 5000; // 5 seconds

      // Store item with TTL
      await storageManager.setItem('ttl-item', testData, { ttl });

      // Verify item is stored
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'ttl-item',
        expect.stringContaining('"ttl":5000')
      );

      // Mock the stored data for retrieval
      const storedCall = mockLocalStorage.setItem.mock.calls.find(call => call[0] === 'ttl-item');
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'ttl-item') return storedCall?.[1] || null;
        return null;
      });

      // Item should be available before expiration
      const retrievedData = await storageManager.getItem('ttl-item');
      expect(retrievedData).toEqual(testData);

      // Fast-forward time past TTL
      jest.advanceTimersByTime(6000);

      // Item should be expired and removed
      const expiredData = await storageManager.getItem('ttl-item');
      expect(expiredData).toBeNull();
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('ttl-item');
    });

    it('should validate TTL values', async () => {
      const testData = { test: 'data' };

      // Test with negative TTL (should be ignored or treated as no TTL)
      await storageManager.setItem('negative-ttl', testData, { ttl: -1000 });
      expect(mockLocalStorage.setItem).toHaveBeenCalled();

      // Test with zero TTL (should expire immediately)
      await storageManager.setItem('zero-ttl', testData, { ttl: 0 });
      const zeroTtlData = await storageManager.getItem('zero-ttl');
      expect(zeroTtlData).toBeNull();
    });

    it('should cleanup expired items during storage operations', async () => {
      // Setup multiple items with different expiration times
      const items = [
        { key: 'expired-1', data: { id: 1 }, ttl: 1000 },
        { key: 'expired-2', data: { id: 2 }, ttl: 2000 },
        { key: 'valid', data: { id: 3 }, ttl: 10000 },
      ];

      // Store all items
      for (const item of items) {
        await storageManager.setItem(item.key, item.data, { ttl: item.ttl });
      }

      // Fast-forward time to expire some items
      jest.advanceTimersByTime(3000);

      // Mock storage to return expired items during cleanup
      mockLocalStorage.length = 3;
      mockLocalStorage.key
        .mockReturnValueOnce('expired-1')
        .mockReturnValueOnce('expired-2')
        .mockReturnValueOnce('valid')
        .mockReturnValue(null);

      mockLocalStorage.getItem.mockImplementation((key) => {
        const now = Date.now();
        switch (key) {
          case 'expired-1':
            return JSON.stringify({ data: { id: 1 }, timestamp: now - 4000, ttl: 1000 });
          case 'expired-2':
            return JSON.stringify({ data: { id: 2 }, timestamp: now - 5000, ttl: 2000 });
          case 'valid':
            return JSON.stringify({ data: { id: 3 }, timestamp: now - 1000, ttl: 10000 });
          default:
            return null;
        }
      });

      // Trigger cleanup by causing quota exceeded error
      mockLocalStorage.setItem
        .mockImplementationOnce(() => {
          const error = new DOMException('Storage quota exceeded', 'QuotaExceededError');
          throw error;
        })
        .mockImplementationOnce(() => {}); // Success after cleanup

      await storageManager.setItem('new-item', { data: 'new' });

      // Should have cleaned up expired items
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('expired-1');
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('expired-2');
      expect(mockLocalStorage.removeItem).not.toHaveBeenCalledWith('valid');
    });
  });

  describe('Storage Encryption and Security', () => {
    it('should encrypt sensitive data before storage', async () => {
      const sensitiveData = {
        password: 'secret123',
        apiKey: 'sk-1234567890',
        personalInfo: { ssn: '***********' }
      };

      await storageManager.setItem('encrypted-data', sensitiveData, { encryption: true });

      // Verify data is encrypted
      const storedCall = mockLocalStorage.setItem.mock.calls.find(call => call[0] === 'encrypted-data');
      expect(storedCall?.[1]).toMatch(/^encrypted:/);
      expect(storedCall?.[1]).not.toContain('secret123');
      expect(storedCall?.[1]).not.toContain('sk-1234567890');
    });

    it('should decrypt data on retrieval', async () => {
      const originalData = { secret: 'confidential' };

      // Store encrypted data
      await storageManager.setItem('decrypt-test', originalData, { encryption: true });

      // Mock encrypted data retrieval
      const encryptedCall = mockLocalStorage.setItem.mock.calls.find(call => call[0] === 'decrypt-test');
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'decrypt-test') return encryptedCall?.[1] || null;
        return null;
      });

      const decryptedData = await storageManager.getItem('decrypt-test');
      expect(decryptedData).toEqual(originalData);
    });

    it('should handle encryption failures gracefully', async () => {
      // Mock encryption failure
      const originalBtoa = global.btoa;
      global.btoa = jest.fn(() => {
        throw new Error('Encryption failed');
      });

      const testData = { data: 'test' };

      // Should not throw, but fall back to unencrypted storage
      await expect(
        storageManager.setItem('encryption-fail', testData, { encryption: true })
      ).resolves.not.toThrow();

      // Restore btoa
      global.btoa = originalBtoa;
    });

    it('should handle decryption failures gracefully', async () => {
      // Mock corrupted encrypted data
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'corrupted-encrypted') {
          return 'encrypted:corrupted-data-that-cannot-be-decrypted';
        }
        return null;
      });

      const result = await storageManager.getItem('corrupted-encrypted');
      expect(result).toBeNull();
    });

    it('should sanitize data before storage', async () => {
      const unsafeData = {
        script: '<script>alert("xss")</script>',
        html: '<img src="x" onerror="alert(1)">',
        normal: 'safe data'
      };

      await storageManager.setItem('sanitized-data', unsafeData);

      // Verify data is stored (sanitization would be implementation-specific)
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'sanitized-data',
        JSON.stringify(unsafeData)
      );
    });
  });

  describe('Cross-Tab Synchronization', () => {
    let mockStorageEvent: jest.MockedFunction<any>;

    beforeEach(() => {
      mockStorageEvent = jest.fn();
      // Mock storage event listener
      Object.defineProperty(window, 'addEventListener', {
        value: jest.fn((event, callback) => {
          if (event === 'storage') {
            mockStorageEvent = callback;
          }
        }),
        writable: true,
      });
    });

    it('should detect storage changes from other tabs', async () => {
      const changeHandler = jest.fn();

      // Setup storage change listener
      storageManager.onStorageChange = changeHandler;

      // Simulate storage event from another tab
      const storageEvent = new StorageEvent('storage', {
        key: 'shared-data',
        newValue: JSON.stringify({ updated: 'from-other-tab' }),
        oldValue: JSON.stringify({ original: 'data' }),
        storageArea: mockLocalStorage as any,
      });

      if (mockStorageEvent) {
        mockStorageEvent(storageEvent);
      }

      expect(changeHandler).toHaveBeenCalledWith({
        key: 'shared-data',
        newValue: { updated: 'from-other-tab' },
        oldValue: { original: 'data' },
        source: 'external',
      });
    });

    it('should broadcast storage changes to other tabs', async () => {
      const testData = { broadcast: 'test' };

      // Mock dispatchEvent to capture broadcast
      const mockDispatchEvent = jest.fn();
      Object.defineProperty(window, 'dispatchEvent', {
        value: mockDispatchEvent,
        writable: true,
      });

      await storageManager.setItem('broadcast-data', testData);

      // Should have dispatched storage event
      expect(mockDispatchEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'storage',
          key: 'broadcast-data',
        })
      );
    });

    it('should handle cross-tab conflicts with last-write-wins strategy', async () => {
      const tab1Data = { source: 'tab1', timestamp: Date.now() };
      const tab2Data = { source: 'tab2', timestamp: Date.now() + 1000 };

      // Simulate concurrent writes
      await storageManager.setItem('conflict-data', tab1Data);

      // Simulate storage event from tab2 with newer timestamp
      const conflictEvent = new StorageEvent('storage', {
        key: 'conflict-data',
        newValue: JSON.stringify(tab2Data),
        oldValue: JSON.stringify(tab1Data),
        storageArea: mockLocalStorage as any,
      });

      if (mockStorageEvent) {
        mockStorageEvent(conflictEvent);
      }

      // Should resolve to newer data
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'conflict-data') return JSON.stringify(tab2Data);
        return null;
      });

      const resolvedData = await storageManager.getItem('conflict-data');
      expect(resolvedData).toEqual(tab2Data);
    });

    it('should maintain data consistency across tabs', async () => {
      const sharedState = {
        counter: 0,
        lastModified: Date.now(),
        modifiedBy: 'tab1',
      };

      // Store initial state
      await storageManager.setItem('shared-state', sharedState);

      // Simulate increment from another tab
      const updatedState = {
        ...sharedState,
        counter: 1,
        lastModified: Date.now() + 1000,
        modifiedBy: 'tab2',
      };

      const updateEvent = new StorageEvent('storage', {
        key: 'shared-state',
        newValue: JSON.stringify(updatedState),
        oldValue: JSON.stringify(sharedState),
        storageArea: mockLocalStorage as any,
      });

      if (mockStorageEvent) {
        mockStorageEvent(updateEvent);
      }

      // Verify state synchronization
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'shared-state') return JSON.stringify(updatedState);
        return null;
      });

      const syncedState = await storageManager.getItem('shared-state');
      expect(syncedState).toEqual(updatedState);
    });
  });

  describe('Storage Migration and Backup', () => {
    it('should export storage data for backup', async () => {
      // Setup test data
      const testData = {
        'user-settings': { theme: 'dark', language: 'en' },
        'app-config': { version: '1.0.0', features: ['feature1'] },
        'cache-data': { temp: 'data' },
      };

      // Mock storage contents
      mockLocalStorage.length = Object.keys(testData).length;
      let keyIndex = 0;
      mockLocalStorage.key.mockImplementation((index) => {
        const keys = Object.keys(testData);
        return keys[index] || null;
      });

      mockLocalStorage.getItem.mockImplementation((key) => {
        return testData[key as keyof typeof testData]
          ? JSON.stringify(testData[key as keyof typeof testData])
          : null;
      });

      const exportedData = await storageManager.exportData();

      expect(exportedData).toEqual({
        version: '1.0.0',
        timestamp: expect.any(Number),
        data: testData,
      });
    });

    it('should import storage data from backup', async () => {
      const backupData = {
        version: '1.0.0',
        timestamp: Date.now(),
        data: {
          'restored-settings': { theme: 'light' },
          'restored-config': { version: '2.0.0' },
        },
      };

      await storageManager.importData(backupData);

      // Should have restored all data
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'restored-settings',
        JSON.stringify({ theme: 'light' })
      );
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'restored-config',
        JSON.stringify({ version: '2.0.0' })
      );
    });

    it('should handle schema migrations', async () => {
      const v1Data = {
        version: 1,
        settings: { theme: 'dark' },
      };

      const v2Schema = {
        version: 2,
        settings: { theme: 'dark', newFeature: true },
        metadata: { migrated: true },
      };

      // Store v1 data
      await storageManager.setItem('schema-data', v1Data);

      // Perform migration
      await storageManager.migrateData('schema-data', v1Data, v2Schema);

      // Should have updated to v2 schema
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'schema-data',
        JSON.stringify(v2Schema)
      );
    });

    it('should validate backup data before import', async () => {
      const invalidBackup = {
        // Missing version
        timestamp: Date.now(),
        data: { 'invalid': 'data' },
      };

      await expect(
        storageManager.importData(invalidBackup)
      ).rejects.toThrow('Invalid backup format');
    });

    it('should handle partial backup restoration', async () => {
      const partialBackup = {
        version: '1.0.0',
        timestamp: Date.now(),
        data: {
          'valid-key': { data: 'valid' },
          'invalid-key': null, // Invalid data
        },
      };

      // Mock setItem to fail for invalid data
      mockLocalStorage.setItem.mockImplementation((key, value) => {
        if (key === 'invalid-key') {
          throw new Error('Invalid data');
        }
      });

      const result = await storageManager.importData(partialBackup, { partial: true });

      expect(result.success).toBe(true);
      expect(result.imported).toBe(1);
      expect(result.failed).toBe(1);
      expect(result.errors).toHaveLength(1);
    });
  });
});
